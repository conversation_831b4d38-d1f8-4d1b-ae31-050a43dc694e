{"errors": {"invalidDate": "Invalid date format", "seatsPositive": "Seats must be positive", "pricePositive": "Price must be positive", "flightExists": "Flight number already exists", "departureBeforeArrival": "departureAirport, arrivalAirport, departureDate, tripType, adults, and cabinClass are required", "returnDateRequired": "returnDate is required for round-trip", "multiCityLegsRequired": "multiCityLegs are required for multi-city", "flightNotFound": "Flight not found", "notEnoughSeats": "Not enough seats available", "seatInventoryChanged": "Seat inventory changed, please refresh", "bookingWindowClosed": "Booking window closed", "priceChanged": "Price changed - please refresh", "amadeusFetchFailed": "Error fetching flights from Amadeus API. Please try again later. Details: {details}", "missingRequiredFields": "Required fields are missing", "tooManyPassengers": "Maximum {max} passengers allowed", "tooManyInfants": "Number of infants cannot exceed number of adults"}, "baggage": {"included": "{count} checked bag", "included_plural": "{count} checked bags"}, "response": {"foundFlights": "Found {paginatedTotal} available flight offers (out of {total} total)"}, "status": {"Scheduled": "Scheduled"}, "duration": {"format": "{hours}h {minutes}m"}, "email": {"newFlightSearchSubject": "New Flight Search", "newFlightSearchBody": "Searched flights ({tripType}) from {departureAirport} to {arrivalAirport} on {departureDate}"}}