import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Stripe from 'stripe';
import { Booking, BookingDocument } from '../../booking/schemas/booking.schema';
import { CreatePaymentIntentDto } from '../dto/create-payment-intent.dto';
import { ConfirmPaymentDto } from '../dto/confirm-payment.dto';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private stripe: Stripe;

  constructor(
    private configService: ConfigService,
    @InjectModel(Booking.name) private bookingModel: Model<BookingDocument>,
  ) {
    const stripeSecretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY is not configured');
    }

    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-02-24.acacia',
    });
  }

  async createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto) {
    const { bookingId, amount, currency, paymentMethodId, customerId } = createPaymentIntentDto;

    try {
      // Find the booking
      const booking = await this.bookingModel.findById(bookingId);
      if (!booking) {
        throw new NotFoundException('Booking not found');
      }

      // Verify the amount matches the booking total price
      const expectedAmount = Math.round(booking.totalPrice * 100); // Convert to cents
      const providedAmount = Math.round(amount * 100);

      if (expectedAmount !== providedAmount) {
        throw new BadRequestException(
          `Amount mismatch. Expected: ${expectedAmount/100}, Provided: ${amount}`
        );
      }

      // Create payment intent
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: expectedAmount,
        currency: currency.toLowerCase(),
        metadata: {
          bookingId: bookingId,
          bookingRef: booking.bookingRef,
        },
        description: `Payment for flight booking ${booking.bookingRef}`,
      };

      // Add customer if provided
      if (customerId) {
        paymentIntentParams.customer = customerId;
      }

      // Add payment method if provided
      if (paymentMethodId) {
        paymentIntentParams.payment_method = paymentMethodId;
        paymentIntentParams.confirmation_method = 'manual';
        paymentIntentParams.confirm = true;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      // Update booking with payment intent ID and set payment status to processing
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        paymentIntentId: paymentIntent.id,
        paymentStatus: 'processing',
        stripeCustomerId: customerId,
      });

      this.logger.log(`Payment intent created: ${paymentIntent.id} for booking: ${bookingId}`);

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret,
        status: paymentIntent.status,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
      };
    } catch (error) {
      this.logger.error(`Failed to create payment intent: ${error.message}`, error.stack);
      throw error;
    }
  }

  async confirmPayment(confirmPaymentDto: ConfirmPaymentDto) {
    const { paymentIntentId, bookingId } = confirmPaymentDto;

    try {
      // Retrieve payment intent from Stripe
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status === 'succeeded') {
        // Update booking status
        const updatedBooking = await this.bookingModel.findByIdAndUpdate(
          bookingId,
          {
            paymentStatus: 'completed',
            status: 'confirmed',
            paymentCompletedAt: new Date(),
          },
          { new: true }
        );

        if (!updatedBooking) {
          throw new NotFoundException('Booking not found');
        }

        this.logger.log(`Payment confirmed for booking: ${bookingId}`);

        return {
          success: true,
          paymentStatus: 'completed',
          bookingStatus: 'confirmed',
          booking: updatedBooking,
        };
      } else {
        // Update payment status to failed
        await this.bookingModel.findByIdAndUpdate(bookingId, {
          paymentStatus: 'failed',
        });

        return {
          success: false,
          paymentStatus: paymentIntent.status,
          message: 'Payment not completed',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to confirm payment: ${error.message}`, error.stack);

      // Update payment status to failed
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        paymentStatus: 'failed',
      });

      throw error;
    }
  }

  async handleWebhook(signature: string, payload: Buffer) {
    const webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');

    try {
      const event = this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);

      this.logger.log(`Received webhook event: ${event.type}`);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        default:
          this.logger.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      this.logger.error(`Webhook error: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
    const bookingId = paymentIntent.metadata.bookingId;

    if (bookingId) {
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        paymentStatus: 'completed',
        status: 'confirmed',
        paymentCompletedAt: new Date(),
      });

      this.logger.log(`Payment succeeded for booking: ${bookingId}`);
    }
  }

  private async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
    const bookingId = paymentIntent.metadata.bookingId;

    if (bookingId) {
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        paymentStatus: 'failed',
      });

      this.logger.log(`Payment failed for booking: ${bookingId}`);
    }
  }

  async getPaymentStatus(bookingId: string) {
    const booking = await this.bookingModel.findById(bookingId);
    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    let stripeStatus = null;
    if (booking.paymentIntentId) {
      try {
        const paymentIntent = await this.stripe.paymentIntents.retrieve(booking.paymentIntentId);
        stripeStatus = paymentIntent.status;
      } catch (error) {
        this.logger.error(`Failed to retrieve payment intent: ${error.message}`);
      }
    }

    return {
      bookingId,
      paymentStatus: booking.paymentStatus,
      bookingStatus: booking.status,
      paymentIntentId: booking.paymentIntentId,
      stripeStatus,
      paymentCompletedAt: booking.paymentCompletedAt,
    };
  }

  async simulatePaymentForTesting(paymentIntentId: string, bookingId: string) {
    try {
      // For testing purposes, we'll use a test payment method to confirm the payment
      const testPaymentMethod = await this.stripe.paymentMethods.create({
        type: 'card',
        card: {
          number: '****************', // Stripe test card
          exp_month: 12,
          exp_year: 2025,
          cvc: '123',
        },
      });

      // Attach the payment method to the payment intent
      await this.stripe.paymentIntents.update(paymentIntentId, {
        payment_method: testPaymentMethod.id,
      });

      // Confirm the payment intent
      const confirmedPaymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId);

      if (confirmedPaymentIntent.status === 'succeeded') {
        // Update booking status
        const updatedBooking = await this.bookingModel.findByIdAndUpdate(
          bookingId,
          {
            paymentStatus: 'completed',
            status: 'confirmed',
            paymentCompletedAt: new Date(),
          },
          { new: true }
        );

        this.logger.log(`Payment simulated and confirmed for booking: ${bookingId}`);

        return {
          success: true,
          paymentStatus: 'completed',
          bookingStatus: 'confirmed',
          stripeStatus: confirmedPaymentIntent.status,
          booking: updatedBooking,
        };
      } else {
        return {
          success: false,
          paymentStatus: confirmedPaymentIntent.status,
          message: 'Payment simulation failed',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to simulate payment: ${error.message}`, error.stack);

      // Update payment status to failed
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        paymentStatus: 'failed',
      });

      throw error;
    }
  }
}
