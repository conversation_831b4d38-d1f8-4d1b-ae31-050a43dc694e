import { ApiProperty } from '@nestjs/swagger';
import {
  IsE<PERSON>,
  IsNotEmpty,
  IsString,
  IsStrongPassword,
  MinLength,
} from 'class-validator';

export class LoginUserDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail()
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'Password123!', description: 'User password' })
  @IsString()
  @IsStrongPassword()
  @MinLength(8)
  password: string;
}
