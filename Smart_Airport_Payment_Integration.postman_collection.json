{"info": {"name": "Smart Airport - Stripe Payment Integration", "description": "Collection for testing Stripe payment integration with Smart Airport booking system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "authToken", "value": "YOUR_JWT_TOKEN_HERE", "type": "string"}, {"key": "bookingId", "value": "", "type": "string"}, {"key": "paymentIntentId", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "1. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.collectionVariables.set('authToken', response.data.accessToken);", "        console.log('Auth token saved:', response.data.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"your-password\"\n}"}, "url": {"raw": "{{baseUrl}}/users/login", "host": ["{{baseUrl}}"], "path": ["users", "login"]}, "description": "Login to get JWT token for authentication"}}]}, {"name": "Booking", "item": [{"name": "2. Create Booking", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.bookingId) {", "        pm.collectionVariables.set('bookingId', response.data.bookingId);", "        console.log('Booking ID saved:', response.data.bookingId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"flightID\": \"F9123\",\n  \"originAirportCode\": \"CAI\",\n  \"destinationAirportCode\": \"JED\",\n  \"originCIty\": \"Cairo\",\n  \"destinationCIty\": \"Jeddah\",\n  \"departureDate\": \"2025-06-15T14:00:00Z\",\n  \"arrivalDate\": \"2025-06-15T18:30:00Z\",\n  \"selectedBaggageOption\": [\n    {\n      \"type\": \"standard\",\n      \"weight\": 20,\n      \"pieces\": 100\n    }\n  ],\n  \"totalPrice\": 450.99,\n  \"applicationFee\": 15.99,\n  \"currency\": \"USD\",\n  \"travellersInfo\": [\n    {\n      \"gender\": \"M\",\n      \"firstName\": \"ahmed\",\n      \"middleName\": \"S\",\n      \"lastName\": \"Sayed\",\n      \"birthDate\": \"1990-01-15\",\n      \"nationality\": \"EGY\",\n      \"passportNumber\": \"********\",\n      \"issuingCountry\": \"EGY\",\n      \"expiryDate\": \"2027-03-05\",\n      \"contactEmail\": \"<EMAIL>\",\n      \"contactPhone\": \"+1234567890\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/booking/book-flight", "host": ["{{baseUrl}}"], "path": ["booking", "book-flight"]}, "description": "Create a new flight booking"}}, {"name": "6. Get My Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/booking/my-bookings", "host": ["{{baseUrl}}"], "path": ["booking", "my-bookings"]}, "description": "Get all bookings for the authenticated user"}}]}, {"name": "Payment", "item": [{"name": "3. Create Payment Intent", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.paymentIntentId) {", "        pm.collectionVariables.set('paymentIntentId', response.data.paymentIntentId);", "        console.log('Payment Intent ID saved:', response.data.paymentIntentId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"{{bookingId}}\",\n  \"amount\": 450.99,\n  \"currency\": \"USD\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["payment", "create-payment-intent"]}, "description": "Create a Stripe payment intent for the booking"}}, {"name": "4. Confirm Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentIntentId\": \"{{paymentIntentId}}\",\n  \"bookingId\": \"{{bookingId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/confirm-payment", "host": ["{{baseUrl}}"], "path": ["payment", "confirm-payment"]}, "description": "Confirm the payment and update booking status"}}, {"name": "5. Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/payment/status/{{bookingId}}", "host": ["{{baseUrl}}"], "path": ["payment", "status", "{{bookingId}}"]}, "description": "Get the current payment status for a booking"}}]}, {"name": "Test Scenarios", "item": [{"name": "Test - Amount <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"{{bookingId}}\",\n  \"amount\": 999.99,\n  \"currency\": \"USD\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["payment", "create-payment-intent"]}, "description": "Test payment intent creation with wrong amount (should fail)"}}, {"name": "Test - Invalid Booking ID", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"invalid-booking-id\",\n  \"amount\": 450.99,\n  \"currency\": \"USD\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["payment", "create-payment-intent"]}, "description": "Test payment intent creation with invalid booking ID (should fail)"}}]}]}